import { FileStream } from "../storage/FileStream";
import { RequestError } from "../core/errors";
import App from "../app";
import { ProductParams, Product } from "./Product";
import ProductPatchJob from "./ProductPatchJob";
import {
  DataNormalizationService,
  NormalizedData,
} from "../core/DataNormalizationService";
import { Chunker } from "../utilities";
import { importFromProductData } from "../reviews/ReviewDataImportService";
import { logger } from "../config/logger";
import { validate } from "../core/validate";

// Extend the normalized error type to include field
interface NormalizedError {
  row: number;
  error: string;
  field?: string;
}

// Define a ValidationError class to better report validation issues
class ValidationError extends Error {
  validationErrors: Array<{ field: string; message: string; row?: number }>;

  constructor(errors: Array<{ field: string; message: string; row?: number }>) {
    super(`Validation failed: ${errors.length} error(s) found`);
    this.name = "ValidationError";
    this.validationErrors = errors;
  }
}

export interface ProductImport {
  location_id: number;
  stream?: FileStream;
  normalization_data?: NormalizedData;
  enhance_with_ai?: boolean;
  reindex?: boolean;
  retailer_id?: string;
  source?: "marketplace" | "upload" | "api";
}

/**
 * Helper method to format prices to proper float values
 * Ensures consistent format with two decimal places for dollar values
 */
export function formatPrice(price: any): number | undefined {
  if (typeof price === "number") {
    return parseFloat(price.toFixed(2));
  } else if (typeof price === "string") {
    const parsed = parseFloat(price);
    return isNaN(parsed) ? undefined : parseFloat(parsed.toFixed(2));
  }
  return undefined;
}

/**
 * Normalizes product data from various sources to ensure consistent format
 * Use this function before passing data to importProducts
 */
export function normalizeProductData(
  products: any[],
  retailerId?: string
): any[] {
  return products.map((product) => {
    // Handle multiple naming conventions for products
    const normalizedProduct = {
      ...product,
      // ID/SKU fields
      meta_sku:
        product.meta_sku ||
        product.id ||
        product.sku ||
        `SUP-${product.external_id || Date.now()}`,

      // Product name fields
      product_name: product.product_name || product.name || product.title,
      raw_product_name:
        product.raw_product_name ||
        product.original_name ||
        product.product_name ||
        product.name,

      // Price fields (formatted consistently)
      latest_price: formatPrice(product.price || product.latest_price) || 0.0,
      wholesale_price: formatPrice(product.wholesale_price),
      retail_price: formatPrice(product.retail_price),
      msrp: formatPrice(product.msrp),

      // Image fields
      image_url:
        product.image_url ||
        (product.images_urls && typeof product.images_urls === "string"
          ? product.images_urls.split(",")[0]
          : undefined) ||
        (product.images &&
        Array.isArray(product.images) &&
        product.images.length > 0
          ? product.images[0]
          : undefined) ||
        "https://via.placeholder.com/150",

      // Category fields
      category: product.category || product.product_category || "other",
      subcategory: product.subcategory || product.product_subcategory,

      // Product visibility - default to active if not specified
      is_active: product.is_active !== undefined ? product.is_active : true,

      // Associate with retailer if provided
      retailer_id: product.retailer_id || retailerId,
    };

    return normalizedProduct;
  });
}

/**
 * Imports products from the marketplace (Supabase) for a given retailer
 */
export async function importFromMarketplace(
  locationId: number,
  retailerId: string,
  products: any[],
  options: {
    enhance_with_ai?: boolean;
    reindex?: boolean;
  } = {}
): Promise<{
  processed: number;
  errors: any[];
  ai_enhanced: boolean;
  reindexed: boolean;
}> {
  logger.info(
    `Importing ${products.length} products from marketplace for retailer ${retailerId}`
  );

  // Normalize the product data
  const normalizedProducts = normalizeProductData(products, retailerId);

  // Create normalized data structure
  const normalizedData = {
    type: "product" as "product",
    data: normalizedProducts,
    errors: [],
  };

  // Import the products
  return await importProducts({
    location_id: locationId,
    normalization_data: normalizedData,
    enhance_with_ai: options.enhance_with_ai ?? true,
    reindex: options.reindex ?? true,
    retailer_id: retailerId,
    source: "marketplace",
  });
}

export const importProducts = async ({
  location_id,
  stream,
  normalization_data,
  enhance_with_ai = false,
  reindex = true,
  retailer_id,
  source = "upload",
}: ProductImport) => {
  if (!location_id || (!stream && !normalization_data)) {
    throw new RequestError(
      "Missing required parameters: need stream or normalized data"
    );
  }

  try {
    // Use provided normalized data or normalize from stream
    let normalizedData: NormalizedData;
    if (normalization_data) {
      normalizedData = normalization_data;
    } else {
      normalizedData = await DataNormalizationService.normalizeData(stream!);
    }

    // Verify this is product data
    if (normalizedData.type !== "product") {
      throw new RequestError(
        "The data is not recognized as product data. Please use the appropriate import endpoint."
      );
    }

    // Report any errors found during normalization
    if (normalizedData.errors.length > 0) {
      const validationErrors = normalizedData.errors.map(
        (err: NormalizedError) => ({
          field: err.field || "unknown",
          message: err.error,
          row: err.row,
        })
      );
      throw new ValidationError(validationErrors);
    }

    // Validate each product against schema
    const validationErrors: Array<{
      field: string;
      message: string;
      row: number;
    }> = [];
    for (let i = 0; i < normalizedData.data.length; i++) {
      const product = normalizedData.data[i] as Partial<ProductParams>;
      try {
        // Perform schema validation on each product
        if (!product.meta_sku) {
          validationErrors.push({
            field: "meta_sku",
            message: "meta_sku is required",
            row: i + 1,
          });
        }
        if (!product.product_name) {
          validationErrors.push({
            field: "product_name",
            message: "product_name is required",
            row: i + 1,
          });
        }
        if (!product.image_url) {
          validationErrors.push({
            field: "image_url",
            message: "image_url is required",
            row: i + 1,
          });
        }
        if (!product.category) {
          validationErrors.push({
            field: "category",
            message: "category is required",
            row: i + 1,
          });
        }
        if (
          product.latest_price === undefined ||
          product.latest_price === null
        ) {
          validationErrors.push({
            field: "latest_price",
            message: "latest_price is required",
            row: i + 1,
          });
        }
      } catch (validationErr) {
        logger.error(`Validation error in row ${i + 1}:`, validationErr);
        validationErrors.push({
          field: "general",
          message:
            validationErr instanceof Error
              ? validationErr.message
              : "Validation error",
          row: i + 1,
        });
      }
    }

    if (validationErrors.length > 0) {
      throw new ValidationError(validationErrors);
    }

    const chunker = new Chunker<ProductPatchJob>(
      (items) => App.main.queue.enqueueBatch(items),
      App.main.queue.batchSize
    );

    let rowCount = 0;
    const processedProducts = [];
    for (const row of normalizedData.data) {
      rowCount++;
      try {
        const product = row as ProductParams;
        if (!product.product_id) {
          product.product_id = product.meta_sku;
        }

        // Apply retailer_id override if provided
        if (retailer_id) {
          product.retailer_id = retailer_id;
        }

        // Ensure price fields are properly formatted
        if (product.latest_price !== undefined) {
          product.latest_price = formatPrice(product.latest_price) || 0.0;
        }
        if (product.wholesale_price !== undefined) {
          product.wholesale_price = formatPrice(product.wholesale_price);
        }
        if (product.retail_price !== undefined) {
          product.retail_price = formatPrice(product.retail_price);
        }
        if (product.msrp !== undefined) {
          product.msrp = formatPrice(product.msrp);
        }

        await chunker.add(
          ProductPatchJob.from({
            location_id,
            product: {
              ...product,
              location_id,
            },
          })
        );
        processedProducts.push(product);
      } catch (error) {
        logger.error(`Error processing row ${rowCount}:`, error);
        continue; // Skip failed rows instead of stopping
      }
    }
    await chunker.flush();

    // Queue AI enhancement as background job if requested (non-blocking)
    if (enhance_with_ai && processedProducts.length > 0) {
      try {
        logger.info(
          `Queueing AI enhancement job for ${processedProducts.length} products`
        );

        // Import the AIEnhancementJob dynamically
        const AIEnhancementJob = require("./AIEnhancementJob").default;

        // Get product IDs from database to pass to enhancement job
        const productIds = processedProducts.map(
          (p: ProductParams) => p.meta_sku
        );
        const dbProducts = await Product.query()
          .select("id")
          .whereIn("meta_sku", productIds)
          .andWhere("location_id", location_id);

        if (dbProducts.length > 0) {
          await App.main.queue.enqueue(
            AIEnhancementJob.from({
              location_id,
              products: dbProducts.map((p: Product) => p.id),
              options: {
                limit: dbProducts.length,
                skip: 0,
                priority: "medium",
              },
            })
          );
          logger.info(
            `AI enhancement job queued for ${dbProducts.length} products`
          );
        } else {
          logger.warn("No products found in database for AI enhancement");
        }
      } catch (aiError) {
        logger.error("Error queueing AI enhancement job:", aiError);
        // Continue - don't fail the import if just the AI enhancement queueing fails
      }
    }

    // Queue reindexing as background job if requested (non-blocking)
    if (reindex && processedProducts.length > 0) {
      try {
        logger.info(
          `Queueing reindexing job for ${processedProducts.length} products in vector database`
        );

        // Import the ProductDataVectorJob dynamically
        const ProductDataVectorJob = require("./ProductDataVectorJob").default;

        await App.main.queue.enqueue(
          ProductDataVectorJob.from({
            location_id,
            batch_size: 100,
            filter: {
              // Filter for recently imported products
              updated_since: new Date(Date.now() - 60000).toISOString(), // Products updated in last minute
            },
          })
        );
        logger.info("Product reindexing job queued successfully");
      } catch (vectorError) {
        logger.error("Error queueing reindexing job:", vectorError);
        // Continue - don't fail the import if just the vector reindexing queueing fails
      }
    } else if (!reindex) {
      logger.info("Skipping product reindexing as reindex=false was specified");
    }

    // Extract and import reviews from product data if they exist
    try {
      const rawData = normalizedData.data;
      // Check if any products contain reviews
      const hasReviews = rawData.some(
        (product: any) =>
          product.reviews || product.review_summary || product.effects
      );

      if (hasReviews) {
        logger.info(
          "Product import contains review data, extracting reviews..."
        );
        await importFromProductData(rawData, reindex);
        logger.info("Reviews extracted and import process started");
      }
    } catch (reviewError) {
      logger.error("Error extracting reviews from product data:", reviewError);
      // Don't fail the product import if review extraction fails
    }

    return {
      processed: rowCount,
      errors: normalizedData.errors,
      ai_enhanced: enhance_with_ai,
      reindexed: reindex,
    };
  } catch (error) {
    logger.error("Error in product import:", error);
    throw error;
  }
};
