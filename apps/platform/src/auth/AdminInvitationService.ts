import { logger } from "../config/logger";
import { loadEmailChannel } from "../providers/email";
import { Email } from "../providers/email/Email";
import Admin from "./Admin";
import Organization from "../organizations/Organization";
import Location from "../locations/Location";
import App from "../app";

export interface AdminInvitationParams {
  admin: Admin;
  organization: Organization;
  invitedBy: Admin;
  location?: Location; // Optional for location-specific invitations
}

export class AdminInvitationService {
  /**
   * Send invitation email to a newly created admin
   */
  static async sendInvitationEmail(params: AdminInvitationParams): Promise<boolean> {
    try {
      const { admin, organization, invitedBy, location } = params;
      
      logger.info(`🚀 Starting invitation email process for admin ${admin.email} (org: ${organization.id}${location ? `, location: ${location.id}` : ''})`);
      logger.info(`👤 Inviting admin details: first_name=${admin.first_name || 'null'}, last_name=${admin.last_name || 'null'}`);
      logger.info(`👥 Invited by: first_name=${invitedBy.first_name || 'null'}, last_name=${invitedBy.last_name || 'null'}, email=${invitedBy.email || 'null'}`);

      // Get the first available email provider for the organization
      // We'll use the first location's email provider or a global one
      const emailProvider = await this.getEmailProvider(organization, location);

      if (!emailProvider) {
        logger.warn(`❌ No email provider available for organization ${organization.id}`);
        logger.warn(`💡 To fix this: Configure an email provider (SendGrid, Mailgun, etc.) in Location Settings → Providers`);
        return false;
      }

      logger.info(`✅ Found email provider: ${emailProvider.provider.constructor.name}`);

      // Generate the invitation email
      const email = this.generateInvitationEmail({
        admin,
        organization,
        invitedBy,
        location,
      });

      logger.info(`📧 Sending invitation email to ${admin.email}...`);
      logger.info(`   Subject: ${email.subject}`);
      logger.info(`   From: ${email.from}`);

      // Send the email
      await emailProvider.provider.send(email);

      logger.info(`✅ Successfully sent invitation email to ${admin.email}`);
      return true;
    } catch (error) {
      logger.error(`❌ Failed to send invitation email to ${params.admin.email}:`, error);
      if (error instanceof Error) {
        logger.error(`   Error message: ${error.message}`);
        logger.error(`   Stack trace: ${error.stack}`);
      }
      return false;
    }
  }

  /**
   * Resend invitation email to an existing admin
   * This is used when an admin wants to resend an invitation to an existing team member
   */
  static async resendInvitationEmail(params: AdminInvitationParams): Promise<boolean> {
    try {
      const { admin, organization, invitedBy, location } = params;

      logger.info(`🔄 Resending invitation email to admin ${admin.email} (org: ${organization.id}${location ? `, location: ${location.id}` : ''})`);
      logger.info(`👤 Resending admin details: first_name=${admin.first_name || 'null'}, last_name=${admin.last_name || 'null'}`);
      logger.info(`👥 Resent by: first_name=${invitedBy.first_name || 'null'}, last_name=${invitedBy.last_name || 'null'}, email=${invitedBy.email || 'null'}`);

      // Prevent users from resending invitations to themselves
      if (admin.id === invitedBy.id) {
        logger.warn(`❌ Admin ${invitedBy.email} attempted to resend invitation to themselves`);
        return false;
      }

      // Get the first available email provider for the organization
      const emailProvider = await this.getEmailProvider(organization, location);

      if (!emailProvider) {
        logger.warn(`❌ No email provider available for organization ${organization.id}`);
        logger.warn(`💡 To fix this: Configure an email provider (SendGrid, Mailgun, etc.) in Location Settings → Providers`);
        return false;
      }

      logger.info(`✅ Found email provider: ${emailProvider.provider.constructor.name}`);

      // Generate the resend invitation email
      const email = this.generateResendInvitationEmail({
        admin,
        organization,
        invitedBy,
        location,
      });

      logger.info(`📧 Resending invitation email to ${admin.email}...`);
      logger.info(`   Subject: ${email.subject}`);
      logger.info(`   From: ${email.from}`);

      // Send the email
      await emailProvider.provider.send(email);

      logger.info(`✅ Successfully resent invitation email to ${admin.email}`);
      return true;
    } catch (error) {
      logger.error(`❌ Failed to resend invitation email to ${params.admin.email}:`, error);
      if (error instanceof Error) {
        logger.error(`   Error message: ${error.message}`);
        logger.error(`   Stack trace: ${error.stack}`);
      }
      return false;
    }
  }

  /**
   * Get an available email provider for sending system emails
   */
  private static async getEmailProvider(organization: Organization, location?: Location) {
    try {
      // If a specific location is provided, try to use its email provider
      if (location) {
        const locationProviders = await this.getLocationEmailProviders(location.id);
        if (locationProviders.length > 0) {
          return await loadEmailChannel(locationProviders[0].id, location.id);
        }
      }

      // Otherwise, try to get any email provider from the organization's locations
      const orgLocations = await Location.all((qb) => 
        qb.where('organization_id', organization.id).limit(10)
      );

      for (const loc of orgLocations) {
        const providers = await this.getLocationEmailProviders(loc.id);
        if (providers.length > 0) {
          const channel = await loadEmailChannel(providers[0].id, loc.id);
          if (channel) return channel;
        }
      }

      return null;
    } catch (error) {
      logger.error('Error getting email provider:', error);
      return null;
    }
  }

  /**
   * Get email providers for a location
   */
  private static async getLocationEmailProviders(locationId: number) {
    const { default: Provider } = await import('../providers/Provider');

    // First try to get default email providers
    let providers = await Provider.all((qb) =>
      qb.where('location_id', locationId)
        .where('group', 'email')
        .where('is_default', true)
        .orderBy('created_at', 'desc')
    );

    // If no default providers, get any email providers
    if (providers.length === 0) {
      logger.info(`No default email providers found for location ${locationId}, trying any email providers`);
      providers = await Provider.all((qb) =>
        qb.where('location_id', locationId)
          .where('group', 'email')
          .orderBy('created_at', 'desc')
      );
    }

    logger.info(`Found ${providers.length} email provider(s) for location ${locationId}`);
    return providers;
  }

  /**
   * Generate the invitation email content
   */
  private static generateInvitationEmail(params: AdminInvitationParams): Email {
    const { admin, organization, invitedBy, location } = params;
    
    const baseUrl = App.main.env.baseUrl || 'https://app.bakedbot.ai';
    const loginUrl = `${baseUrl}/auth/login`;
    
    const subject = `🚀 You’ve been invited to join BakedBot AI – Let’s Revolutionize Cannabis Together`

    const htmlContent = this.generateHtmlTemplate({
      admin,
      organization,
      invitedBy,
      location,
      loginUrl,
    });

    const textContent = this.generateTextTemplate({
      admin,
      organization,
      invitedBy,
      location,
      loginUrl,
    });

    return {
      to: admin.email,
      from: '<EMAIL>', // Using verified sender address
      subject,
      html: htmlContent,
      text: textContent,
    };
  }

  /**
   * Generate the resend invitation email content
   */
  private static generateResendInvitationEmail(params: AdminInvitationParams): Email {
    const { admin, organization, invitedBy, location } = params;

    const baseUrl = App.main.env.baseUrl || 'https://app.bakedbot.ai';
    const loginUrl = `${baseUrl}/auth/login`;

    const subject = `🚀 You’ve been invited to join BakedBot AI – Let’s Revolutionize Cannabis Together`

    const htmlContent = this.generateResendHtmlTemplate({
      admin,
      organization,
      invitedBy,
      location,
      loginUrl,
    });

    const textContent = this.generateResendTextTemplate({
      admin,
      organization,
      invitedBy,
      location,
      loginUrl,
    });

    return {
      to: admin.email,
      from: '<EMAIL>', // Using verified sender address
      subject,
      html: htmlContent,
      text: textContent,
    };
  }

  /**
   * Generate HTML email template
   */
  private static generateHtmlTemplate(params: {
    admin: Admin;
    organization: Organization;
    invitedBy: Admin;
    location?: Location;
    loginUrl: string;
  }): string {
    const { admin, organization, invitedBy, location, loginUrl } = params;

    const greeting = admin.first_name ? `Hey ${admin.first_name}` : 'Hello';

    // Build inviter name with fallbacks
    let inviterName = 'Your team admin';
    if (invitedBy.first_name && invitedBy.last_name) {
      inviterName = `${invitedBy.first_name} ${invitedBy.last_name}`;
    } else if (invitedBy.first_name) {
      inviterName = invitedBy.first_name;
    } else if (invitedBy.email) {
      inviterName = invitedBy.email;
    }

    // Ensure we have a valid email for contact info
    const inviterEmail = invitedBy.email || '<EMAIL>';

    const contextText = location
      ? `You’ve been handpicked by <strong>${location.name}</strong> to join the BakedBot AI platform – where AI meets cannabis retail to transform how the industry works.`
      : `You’ve been handpicked by <strong>${organization.username}</strong> to join the BakedBot AI platform – where AI meets cannabis retail to transform how the industry works.`;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>BakedBot Access Reminder</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
          .content { background: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
          .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
          .footer { text-align: center; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="content">
            <p>${greeting}!</p>

            <p>${contextText}</p>

            <p>🔥 Why this matters:<br>You’re about to get early access to the tools that power autonomous cannabis commerce – including smart agents like Smokey (AI Budtender), Craig (Marketing Automation), and Pops (Business Intelligence). This is your chance to shape the future of cannabis tech from the inside.</p>

            <p>🛠️ What’s Next?<br>Click below to activate your account and join the movement:</p>

            <p style="text-align: left; margin: 30px 0;">
              👉 <a href="${loginUrl}" class="button">Accept Your Invite</a>
            </p>
          </div>

          <div class="footer">
            <p>This link expires in 48 hours – so don’t sleep on it. Welcome to the front lines of retail disruption.</p>
            <p>Let’s build the future together,<br>— The BakedBot AI Team<br><a href="https://bakedbot.ai/">www.bakedbot.ai</a></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate plain text email template
   */
  private static generateTextTemplate(params: {
    admin: Admin;
    organization: Organization;
    invitedBy: Admin;
    location?: Location;
    loginUrl: string;
  }): string {
    const { admin, organization, invitedBy, location, loginUrl } = params;

    const greeting = admin.first_name ? `Hey ${admin.first_name}` : 'Hello';

    // Build inviter name with fallbacks
    let inviterName = 'Your team admin';
    if (invitedBy.first_name && invitedBy.last_name) {
      inviterName = `${invitedBy.first_name} ${invitedBy.last_name}`;
    } else if (invitedBy.first_name) {
      inviterName = invitedBy.first_name;
    } else if (invitedBy.email) {
      inviterName = invitedBy.email;
    }

    // Ensure we have a valid email for contact info
    const inviterEmail = invitedBy.email || '<EMAIL>';

    const contextText = location
      ? `You’ve been handpicked by <strong>${location.name}</strong> to join the BakedBot AI platform – where AI meets cannabis retail to transform how the industry works.`
      : `You’ve been handpicked by <strong>${organization.username}</strong> to join the BakedBot AI platform – where AI meets cannabis retail to transform how the industry works.`;

    return `
${greeting}!

${contextText}

🔥 Why this matters:
You’re about to get early access to the tools that power autonomous cannabis commerce – including smart agents like Smokey (AI Budtender), Craig (Marketing Automation), and Pops (Business Intelligence). This is your chance to shape the future of cannabis tech from the inside.

🛠️ What’s Next?
Click below to activate your account and join the movement:

👉 ${loginUrl}

This link expires in 48 hours – so don’t sleep on it. Welcome to the front lines of retail disruption.
Let’s build the future together,
— The BakedBot AI Team
www.bakedbot.ai
    `.trim();
  }

  /**
   * Generate HTML email template for resend invitations
   */
  private static generateResendHtmlTemplate(params: {
    admin: Admin;
    organization: Organization;
    invitedBy: Admin;
    location?: Location;
    loginUrl: string;
  }): string {
    const { admin, organization, invitedBy, location, loginUrl } = params;

    const greeting = admin.first_name ? `Hey ${admin.first_name}` : 'Hello';

    // Build inviter name with fallbacks
    let inviterName = 'Your team admin';
    if (invitedBy.first_name && invitedBy.last_name) {
      inviterName = `${invitedBy.first_name} ${invitedBy.last_name}`;
    } else if (invitedBy.first_name) {
      inviterName = invitedBy.first_name;
    } else if (invitedBy.email) {
      inviterName = invitedBy.email;
    }

    // Ensure we have a valid email for contact info
    const inviterEmail = invitedBy.email || '<EMAIL>';

    const contextText = location
      ? `You’ve been handpicked by <strong>${location.name}</strong> to join the BakedBot AI platform – where AI meets cannabis retail to transform how the industry works.`
      : `You’ve been handpicked by <strong>${organization.username}</strong> to join the BakedBot AI platform – where AI meets cannabis retail to transform how the industry works.`;

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>BakedBot Access Reminder</title>
        <style>
          body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
          .content { background: #f9fafb; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
          .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
          .footer { text-align: center; color: #6b7280; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="content">
            <p>${greeting}!</p>

            <p>${contextText}</p>

            <p>🔥 Why this matters:<br>You’re about to get early access to the tools that power autonomous cannabis commerce – including smart agents like Smokey (AI Budtender), Craig (Marketing Automation), and Pops (Business Intelligence). This is your chance to shape the future of cannabis tech from the inside.</p>

            <p>🛠️ What’s Next?<br>Click below to activate your account and join the movement:</p>

            <p style="text-align: left; margin: 30px 0;">
              👉 <a href="${loginUrl}" class="button">Accept Your Invite</a>
            </p>
          </div>

          <div class="footer">
            <p>This link expires in 48 hours – so don’t sleep on it. Welcome to the front lines of retail disruption.</p>
            <p>Let’s build the future together,<br>— The BakedBot AI Team<br><a href="https://bakedbot.ai/">www.bakedbot.ai</a></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate plain text email template for resend invitations
   */
  private static generateResendTextTemplate(params: {
    admin: Admin;
    organization: Organization;
    invitedBy: Admin;
    location?: Location;
    loginUrl: string;
  }): string {
    const { admin, organization, invitedBy, location, loginUrl } = params;

    const greeting = admin.first_name ? `Hey ${admin.first_name}` : 'Hello';

    // Build inviter name with fallbacks
    let inviterName = 'Your team admin';
    if (invitedBy.first_name && invitedBy.last_name) {
      inviterName = `${invitedBy.first_name} ${invitedBy.last_name}`;
    } else if (invitedBy.first_name) {
      inviterName = invitedBy.first_name;
    } else if (invitedBy.email) {
      inviterName = invitedBy.email;
    }

    // Ensure we have a valid email for contact info
    const inviterEmail = invitedBy.email || '<EMAIL>';

    const contextText = location
      ? `You’ve been handpicked by <strong>${location.name}</strong> to join the BakedBot AI platform – where AI meets cannabis retail to transform how the industry works.`
      : `You’ve been handpicked by <strong>${organization.username}</strong> to join the BakedBot AI platform – where AI meets cannabis retail to transform how the industry works.`;

    return `
${greeting}!

${contextText}

🔥 Why this matters:
You’re about to get early access to the tools that power autonomous cannabis commerce – including smart agents like Smokey (AI Budtender), Craig (Marketing Automation), and Pops (Business Intelligence). This is your chance to shape the future of cannabis tech from the inside.

🛠️ What’s Next?
Click below to activate your account and join the movement:

👉 ${loginUrl}

This link expires in 48 hours – so don’t sleep on it. Welcome to the front lines of retail disruption.
Let’s build the future together,
— The BakedBot AI Team
www.bakedbot.ai
    `.trim();
  }
}
