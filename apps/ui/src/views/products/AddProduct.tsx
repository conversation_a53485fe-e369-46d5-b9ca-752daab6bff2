import { useContext, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { LocationContext } from "../../contexts";
import PageContent from "../../ui/PageContent";
import { useTranslation } from "react-i18next";
import api from "../../api";
import Button from "../../ui/Button";
import Card from "../../ui/Card";
import TextInput from "../../ui/form/TextInput";
import CheckboxInput from "../../ui/form/CheckboxInput";
import { ProductParams } from "../../types";
import { validateProductForAI } from "../../utils/productValidation";
import "./AddProduct.css";
import { FiChevronDown, FiChevronUp } from "react-icons/fi";

// Extended interface for form data to include featured product flags
interface ExtendedProductParams extends Partial<ProductParams> {
  is_featured?: boolean;
  is_top_pick?: boolean;
}

export default function AddProduct() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [location] = useContext(LocationContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showAdditionalDetails, setShowAdditionalDetails] = useState(false);

  const [formData, setFormData] = useState<ExtendedProductParams>({
    meta_sku: "",
    retailer_id: "",
    raw_product_name: "",
    product_name: "",
    product_description: "",
    effects: "",
    mood: [],
    medical: false,
    recreational: true,
    is_featured: false,
    is_top_pick: false,
  });

  const handleInputChange = (
    name: string,
    value: string | boolean | number | undefined | string[]
  ) => {
    let processedValue = value;

    // Convert comma-separated strings to arrays for mood and effects
    if (name === "mood" && typeof value === "string") {
      processedValue = value
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item.length > 0);
    } else if (name === "effects" && typeof value === "string") {
      processedValue = value
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item.length > 0);
    }

    setFormData((prev) => ({
      ...prev,
      [name]: processedValue,
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setImageFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const clearImage = () => {
    setImageFile(null);
    setImagePreview(null);
    // Clear the file input value
    const fileInput = document.querySelector(
      'input[type="file"]'
    ) as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!location) {
      setError("No location selected");
      return;
    }

    // Validate required fields according to backend validation
    const requiredFields = [
      { field: "meta_sku", label: "Meta SKU" },
      { field: "product_name", label: "Product Name" },
      { field: "category", label: "Category" },
      { field: "latest_price", label: "Price" },
    ];

    const missingFields = requiredFields.filter(({ field }) => {
      const value = formData[field as keyof typeof formData];
      return !value || (typeof value === "string" && value.trim() === "");
    });

    // Check for image requirement - either uploaded file or image URL
    const hasImage =
      imageFile || (formData.image_url && formData.image_url.trim() !== "");
    if (!hasImage) {
      missingFields.push({
        field: "image",
        label: "Product Image (file or URL)",
      });
    }

    if (missingFields.length > 0) {
      const fieldNames = missingFields.map((f) => f.label).join(", ");
      setError(`The following required fields are missing: ${fieldNames}`);
      return;
    }

    // Validate product has sufficient data for AI indexing (warn but don't block)
    const aiStatus = validateProductForAI(formData);
    if (aiStatus.status === "needs-improvement") {
      const suggestionsText =
        aiStatus.suggestions && aiStatus.suggestions.length > 0
          ? `\n\nSuggestions:\n• ${aiStatus.suggestions.join("\n• ")}`
          : "";

      const userConfirmed = window.confirm(
        `⚠️ ${aiStatus.label}\n\n` +
          `${aiStatus.description}${suggestionsText}\n\n` +
          `Do you want to create the product anyway?`
      );

      if (!userConfirmed) {
        return; // Don't submit if user wants to add more info
      }
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Extract featured product flags before preparing product data
      const { is_featured, is_top_pick, ...productFormData } = formData;

      // Prepare product data
      const productData: ProductParams = {
        ...(productFormData as ProductParams),
        location_id: location.id,
        // If raw_product_name is not provided, use product_name
        raw_product_name:
          productFormData.raw_product_name ||
          productFormData.product_name ||
          "",
      };

      // Create FormData for multipart request or use JSON
      if (imageFile) {
        // Use multipart form data with file upload
        const formDataToSend = new FormData();

        // Add all product data fields
        Object.entries(productData).forEach(([key, value]) => {
          if (value !== null && value !== undefined) {
            if (Array.isArray(value)) {
              formDataToSend.append(key, JSON.stringify(value));
            } else {
              formDataToSend.append(key, String(value));
            }
          }
        });

        // Add the image file
        formDataToSend.append("productImage", imageFile);

        // Create product with image in one request
        const response = await fetch(
          `/api/admin/locations/${location.id}/products`,
          {
            method: "POST",
            body: formDataToSend,
            headers: {
              Accept: "application/json",
            },
            credentials: "include",
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.error || `Request failed: ${response.statusText}`
          );
        }

        const createdProduct = await response.json();
        console.log("Product created successfully:", createdProduct);

        // Add to featured products if requested
        if (is_featured) {
          try {
            await api.featuredProducts.addFeatured(location.id, {
              product_id: createdProduct.id,
              is_top_pick: !!is_top_pick,
              active: true,
            });
            console.log("Product added to featured list");
          } catch (featuredError) {
            console.error(
              "Error adding product to featured list:",
              featuredError
            );
            // Don't fail the whole operation for this
          }
        }

        setSuccess(true);

        // Navigate to the created product's detail page after a short delay
        setTimeout(() => {
          navigate(`/locations/${location.id}/products/${createdProduct.id}`);
        }, 1000);

        return; // Exit early since we have the product
      } else if (formData.image_url) {
        // Use download_image_url for URL-based images
        const productDataWithDownload = {
          ...productData,
          download_image_url: productFormData.image_url,
        };
        delete productDataWithDownload.image_url; // Remove image_url since we're using download_image_url

        // Create product with image download in one request
        const createdProduct = await api.products.create(
          location.id,
          productDataWithDownload
        );
        console.log("Product created successfully:", createdProduct);

        // Add to featured products if requested
        if (is_featured) {
          try {
            await api.featuredProducts.addFeatured(location.id, {
              product_id: createdProduct.id,
              is_top_pick: !!is_top_pick,
              active: true,
            });
            console.log("Product added to featured list");
          } catch (featuredError) {
            console.error(
              "Error adding product to featured list:",
              featuredError
            );
            // Don't fail the whole operation for this
          }
        }

        setSuccess(true);

        // Navigate to the created product's detail page after a short delay
        setTimeout(() => {
          navigate(`/locations/${location.id}/products/${createdProduct.id}`);
        }, 1000);

        return; // Exit early since we have the product
      } else {
        // This shouldn't happen due to validation, but handle it gracefully
        throw new Error("No image provided");
      }
    } catch (error) {
      console.error("Error creating product:", error);

      // Handle specific validation errors from backend
      if (error instanceof Error) {
        if (
          error.message.includes("meta_sku") &&
          error.message.includes("already exists")
        ) {
          setError(
            "A product with this Meta SKU already exists. Please use a different Meta SKU."
          );
        } else if (error.message.includes("validation")) {
          setError(`Validation error: ${error.message}`);
        } else {
          setError(`Failed to create product: ${error.message}`);
        }
      } else {
        setError("Failed to create product. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(`/locations/${location.id}/products`);
  };

  const toggleAdditionalDetails = () => {
    setShowAdditionalDetails(!showAdditionalDetails);
  };

  return (
    <PageContent title={t("Add New Product")}>
      <form onSubmit={handleSubmit} className="add-product-form">
        <Card>
          <div className="card-content">
            <div className="form-note">
              <p>
                <strong>Required fields:</strong> Product Name, Meta SKU,
                Category, Price, and Product Image
              </p>
            </div>

            {/* AI Readiness Indicator */}
            {(() => {
              const aiStatus = validateProductForAI(formData);
              const isReady =
                aiStatus.status === "ready" || aiStatus.status === "enhanced";
              return (
                <div
                  className={`ai-readiness-indicator ${
                    isReady ? "ai-ready" : "ai-needs-improvement"
                  }`}
                >
                  <div className="ai-status">
                    <div
                      className={`ai-status-icon ${
                        isReady ? "ready" : "warning"
                      }`}
                    >
                      {aiStatus.icon}
                    </div>
                    <div className="ai-status-text">
                      <strong>AI Budtender Status:</strong> {aiStatus.label}
                    </div>
                  </div>
                  {!isReady && (
                    <div className="ai-improvement-hint">
                      {aiStatus.description} Adding more details helps Smokey
                      provide better recommendations to customers.
                      {aiStatus.suggestions &&
                        aiStatus.suggestions.length > 0 && (
                          <div className="mt-1">
                            <strong>Suggestions:</strong>{" "}
                            {aiStatus.suggestions.join(", ")}
                          </div>
                        )}
                    </div>
                  )}
                </div>
              );
            })()}

            <div className="form-fields">
              {/* Basic Information Section */}
              <div className="form-section">
                <h3>{t("Basic Information")}</h3>
                <div className="form-section-content">
                  <div className="form-row">
                    <TextInput
                      label={t("Product Name")}
                      name="product_name"
                      value={formData.product_name || ""}
                      onChange={(value) =>
                        handleInputChange("product_name", value)
                      }
                      required
                    />
                    <TextInput
                      label={t("Brand Name")}
                      name="brand_name"
                      value={formData.brand_name || ""}
                      onChange={(value) =>
                        handleInputChange("brand_name", value)
                      }
                    />
                  </div>

                  <TextInput
                    label={t("Description")}
                    name="product_description"
                    textarea
                    value={formData.product_description || ""}
                    onChange={(value) =>
                      handleInputChange("product_description", value)
                    }
                    subtitle={t("Enter a detailed description of the product")}
                  />

                  <div className="form-row">
                    <TextInput
                      label={t("Effects")}
                      name="effects"
                      value={
                        Array.isArray(formData.effects)
                          ? formData.effects.join(", ")
                          : typeof formData.effects === "string"
                          ? formData.effects
                          : ""
                      }
                      onChange={(value) => handleInputChange("effects", value)}
                      subtitle={t("e.g., Relaxed, Euphoric, Happy")}
                    />
                    <TextInput
                      label={t("Mood")}
                      name="mood"
                      value={
                        Array.isArray(formData.mood)
                          ? formData.mood.join(", ")
                          : typeof formData.mood === "string"
                          ? formData.mood
                          : ""
                      }
                      onChange={(value) => handleInputChange("mood", value)}
                      subtitle={t("e.g., Sleepy, Energetic, Creative")}
                    />
                  </div>

                  <div className="form-row">
                    <TextInput
                      label={t("Category")}
                      name="category"
                      value={formData.category || ""}
                      onChange={(value) => handleInputChange("category", value)}
                      required
                    />
                    <TextInput
                      label={t("Subcategory")}
                      name="subcategory"
                      value={formData.subcategory || ""}
                      onChange={(value) =>
                        handleInputChange("subcategory", value)
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Image Section */}
              <div className="form-section">
                <h3>{t("Product Image")} *</h3>
                <div className="form-section-content">
                  <div className="image-upload-container">
                    <div className="image-upload-header">
                      <div className="image-upload-buttons">
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          className="hidden-file-input"
                          onChange={handleImageChange}
                          id="product-image"
                        />
                        <Button
                          type="button"
                          onClick={() => fileInputRef.current?.click()}
                          size="small"
                        >
                          {t("Select Image")}
                        </Button>
                        {imagePreview && (
                          <Button
                            type="button"
                            onClick={clearImage}
                            variant="secondary"
                            size="small"
                          >
                            {t("Clear")}
                          </Button>
                        )}
                      </div>
                    </div>

                    <div className="image-preview-container">
                      {imagePreview ? (
                        <img
                          src={imagePreview}
                          alt="Product Preview"
                          className="image-preview"
                        />
                      ) : (
                        <div className="image-placeholder">
                          {t("No image selected")}
                        </div>
                      )}
                    </div>

                    <div className="image-url-input">
                      <TextInput
                        label={t("Or enter image URL")}
                        name="image_url"
                        value={formData.image_url || ""}
                        onChange={(value) =>
                          handleInputChange("image_url", value)
                        }
                        disabled={!!imageFile}
                        subtitle={
                          imageFile
                            ? t("Clear uploaded image to use URL")
                            : t(
                                "Image is required - upload a file or enter URL"
                              )
                        }
                        required={!imageFile}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Product Details Section */}
              <div className="form-section">
                <h3>{t("Product Details")}</h3>
                <div className="form-section-content">
                  <div className="form-row">
                    <TextInput
                      label={t("THC %")}
                      name="percentage_thc"
                      type="number"
                      value={formData.percentage_thc?.toString() || ""}
                      onChange={(value) =>
                        handleInputChange(
                          "percentage_thc",
                          value ? parseFloat(value) : undefined
                        )
                      }
                    />
                    <TextInput
                      label={t("CBD %")}
                      name="percentage_cbd"
                      type="number"
                      value={formData.percentage_cbd?.toString() || ""}
                      onChange={(value) =>
                        handleInputChange(
                          "percentage_cbd",
                          value ? parseFloat(value) : undefined
                        )
                      }
                    />
                    <TextInput
                      label={t("Price")}
                      name="latest_price"
                      type="number"
                      value={formData.latest_price?.toString() || ""}
                      onChange={(value) =>
                        handleInputChange(
                          "latest_price",
                          value ? parseFloat(value) : undefined
                        )
                      }
                      required
                    />
                  </div>

                  <div className="form-row checkbox-container">
                    <CheckboxInput
                      label={t("Medical")}
                      checked={!!formData.medical}
                      value={!!formData.medical}
                      onChange={(value) => handleInputChange("medical", value)}
                    />
                    <CheckboxInput
                      label={t("Recreational")}
                      checked={!!formData.recreational}
                      value={!!formData.recreational}
                      onChange={(value) =>
                        handleInputChange("recreational", value)
                      }
                    />
                  </div>
                </div>
              </div>

              {/* Product IDs Section */}
              <div className="form-section">
                <h3>{t("Product IDs")}</h3>
                <div className="form-section-content">
                  <div className="form-row">
                    <TextInput
                      label={t("Meta SKU")}
                      name="meta_sku"
                      value={formData.meta_sku || ""}
                      onChange={(value) => handleInputChange("meta_sku", value)}
                      required
                    />
                    <TextInput
                      label={t("Retailer ID")}
                      name="retailer_id"
                      value={formData.retailer_id || ""}
                      onChange={(value) =>
                        handleInputChange("retailer_id", value)
                      }
                      subtitle={t(
                        "Optional - defaults to location ID if not provided"
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Featured Products Section */}
              <div className="form-section">
                <h3>Featured Products</h3>
                <div className="form-section-content">
                  <div className="form-row checkbox-container">
                    <CheckboxInput
                      label="Mark as Featured Product"
                      checked={!!formData.is_featured}
                      value={!!formData.is_featured}
                      onChange={(value) =>
                        handleInputChange("is_featured", value)
                      }
                    />
                    <CheckboxInput
                      label="Mark as Top Pick"
                      checked={!!formData.is_top_pick}
                      value={!!formData.is_top_pick}
                      onChange={(value) =>
                        handleInputChange("is_top_pick", value)
                      }
                      disabled={!formData.is_featured}
                    />
                  </div>
                </div>
              </div>

              {/* Additional Details (Expandable) Section */}
              <div className="form-section expandable">
                <div
                  className="expandable-header"
                  onClick={toggleAdditionalDetails}
                >
                  <h3>{t("Additional Details")}</h3>
                  <span className="expandable-icon">
                    {showAdditionalDetails ? (
                      <FiChevronUp />
                    ) : (
                      <FiChevronDown />
                    )}
                  </span>
                </div>

                {showAdditionalDetails && (
                  <div className="form-section-content">
                    <div className="form-row">
                      <TextInput
                        label={t("Weight")}
                        name="display_weight"
                        value={formData.display_weight || ""}
                        onChange={(value) =>
                          handleInputChange("display_weight", value)
                        }
                      />
                      <TextInput
                        label={t("Quantity Per Package")}
                        name="quantity_per_package"
                        type="number"
                        value={formData.quantity_per_package?.toString() || ""}
                        onChange={(value) =>
                          handleInputChange(
                            "quantity_per_package",
                            value ? parseFloat(value) : undefined
                          )
                        }
                      />
                    </div>

                    <div className="form-row">
                      <TextInput
                        label={t("THC (mg)")}
                        name="mg_thc"
                        type="number"
                        value={formData.mg_thc?.toString() || ""}
                        onChange={(value) =>
                          handleInputChange(
                            "mg_thc",
                            value ? parseFloat(value) : undefined
                          )
                        }
                      />
                      <TextInput
                        label={t("CBD (mg)")}
                        name="mg_cbd"
                        type="number"
                        value={formData.mg_cbd?.toString() || ""}
                        onChange={(value) =>
                          handleInputChange(
                            "mg_cbd",
                            value ? parseFloat(value) : undefined
                          )
                        }
                      />
                    </div>

                    <TextInput
                      label={t("Menu Provider")}
                      name="menu_provider"
                      value={formData.menu_provider || ""}
                      onChange={(value) =>
                        handleInputChange("menu_provider", value)
                      }
                    />

                    <TextInput
                      label={t("Product URL")}
                      name="url"
                      value={formData.url || ""}
                      onChange={(value) => handleInputChange("url", value)}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>

        <div className="form-actions">
          <Button variant="secondary" onClick={handleCancel}>
            {t("cancel")}
          </Button>
          <Button type="submit" disabled={isSubmitting || success}>
            {success
              ? t("Created! Redirecting...")
              : isSubmitting
              ? t("Creating Product...")
              : t("Create Product")}
          </Button>
        </div>
      </form>

      {error && <div className="error-message">{error}</div>}

      {success && (
        <div className="success-message">
          <div className="success-content">
            <span className="success-icon">✅</span>
            <div className="success-text">
              <strong>{t("Product created successfully!")}</strong>
              <p>{t("Redirecting to product details...")}</p>
            </div>
          </div>
        </div>
      )}
    </PageContent>
  );
}
