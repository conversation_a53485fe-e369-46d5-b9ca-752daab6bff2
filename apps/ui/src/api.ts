import Axios from "axios";
import { env } from "./config/env";
import {
  Admin,
  AuthMethod,
  Campaign,
  CampaignCreateParams,
  CampaignLaunchParams,
  CampaignUpdateParams,
  CampaignUser,
  Image,
  Journey,
  JourneyEntranceDetail,
  JourneyStepMap,
  JourneyUserStep,
  List,
  ListCreateParams,
  ListUpdateParams,
  Locale,
  Metric,
  Organization,
  OrganizationUpdateParams,
  Location,
  LocationAdmin,
  LocationAdminInviteParams,
  LocationAdminParams,
  LocationApiKey,
  LocationApiKeyParams,
  Provider,
  ProviderCreateParams,
  ProviderMeta,
  ProviderUpdateParams,
  QueueMetric,
  Resource,
  RuleSuggestions,
  SearchParams,
  SearchResult,
  Subscription,
  SubscriptionCreateParams,
  SubscriptionParams,
  SubscriptionUpdateParams,
  Tag,
  Template,
  TemplateCreateParams,
  TemplatePreviewParams,
  TemplateProofParams,
  TemplateUpdateParams,
  User,
  UserEvent,
  UserSubscription,
  POSData,
  Product,
  ProductParams,
  BulkDeleteResponse,
  Insight,
  InsightStatus,
  Competitor,
  PosConnectionResult,
  SupabaseUploadResult,
  CoaData,
  Order,
} from "./types";
import { get } from "https";
import systemModule from "./api/system";

enum SubscriptionStatus {
  incomplete = "incomplete",
  incompleteExpired = "incomplete_expired",
  trailing = "trailing",
  active = "active",
  pastDue = "past_due",
  canceled = "canceled",
  unpaid = "unpaid",
  paused = "paused",
}

interface StripeSubscription {
  organization_id: number;
  subscription_id?: string;
  customer_id?: string;
  price_id?: string;
  product_id?: string;
  status: SubscriptionStatus;
  curreny_period_start?: Date;
  current_period_end?: Date;
  canceled_at?: Date;
  trial_start?: Date;
  trial_end?: Date;
  quantity?: number;
}
interface StripeSubscriptionResponse {
  subscription: StripeSubscription;
}

function appendValue(params: URLSearchParams, name: string, value: unknown) {
  if (
    typeof value === "undefined" ||
    value === null ||
    typeof value === "function"
  )
    return;
  if (typeof value === "object") value = JSON.stringify(value);
  params.append(name, value + "");
}

export const client = Axios.create({
  ...env.api,
  withCredentials: true,
  paramsSerializer: (params) => {
    const s = new URLSearchParams();
    for (const [name, value] of Object.entries(params)) {
      if (Array.isArray(value)) {
        for (const item of value) {
          appendValue(s, name, item);
        }
      } else {
        appendValue(s, name, value);
      }
    }
    return s.toString();
  },
});

client.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response.status === 401) {
      api.auth.login();
    }
    if (error.response.status === 403) {
      api.auth.clearRecentLocations();
      window.location.href = "/organization/locations";
      return Promise.reject(error);
    }
    throw error;
  }
);

export interface NetworkError {
  response: {
    data: any;
    status: number;
  };
}

type OmitFields =
  | "id"
  | "created_at"
  | "updated_at"
  | "deleted_at"
  | "stats"
  | "stats_at";

export interface EntityApi<T> {
  basePath: string;
  search: (params: Partial<SearchParams>) => Promise<SearchResult<T>>;
  create: (params: Omit<T, OmitFields>) => Promise<T>;
  get: (id: number | string) => Promise<T>;
  update: (id: number | string, params: Omit<T, OmitFields>) => Promise<T>;
  delete: (id: number | string) => Promise<number>;
}

function createEntityPath<T>(basePath: string): EntityApi<T> {
  return {
    basePath,
    search: async (params) =>
      await client
        .get<SearchResult<T>>(basePath, { params })
        .then((r) => r.data),
    create: async (params) =>
      await client.post<T>(basePath, params).then((r) => r.data),
    get: async (id) =>
      await client.get<T>(`${basePath}/${id}`).then((r) => r.data),
    update: async (id, params) =>
      await client.patch<T>(`${basePath}/${id}`, params).then((r) => r.data),
    delete: async (id) =>
      await client.delete<number>(`${basePath}/${id}`).then((r) => r.data),
  };
}

export interface LocationEntityPath<
  T,
  C = Omit<T, OmitFields>,
  U = Omit<T, OmitFields>
> {
  prefix: string;
  search: (
    locationId: number | string,
    params: SearchParams
  ) => Promise<SearchResult<T>>;
  create: (locationId: number | string, params: C) => Promise<T>;
  get: (locationId: number | string, id: number | string) => Promise<T>;
  update: (
    locationId: number | string,
    id: number | string,
    params: U
  ) => Promise<T>;
  delete: (locationId: number | string, id: number | string) => Promise<number>;
}

const locationUrl = (locationId: number | string): string =>
  `/admin/locations/${locationId}`;

export const apiUrl = (locationId: number | string, path: string) =>
  `${env.api.baseURL}${locationUrl(locationId)}/${path}`;

function createLocationEntityPath<
  T,
  C = Omit<T, OmitFields>,
  U = Omit<T, OmitFields>
>(prefix: string): LocationEntityPath<T, C, U> {
  return {
    prefix,
    search: async (locationId, params) =>
      await client
        .get<SearchResult<T>>(`${locationUrl(locationId)}/${prefix}`, {
          params,
        })
        .then((r) => r.data),
    create: async (locationId, params) =>
      await client
        .post<T>(`${locationUrl(locationId)}/${prefix}`, params)
        .then((r) => r.data),
    get: async (locationId, entityId) =>
      await client
        .get<T>(`${locationUrl(locationId)}/${prefix}/${entityId}`)
        .then((r) => r.data),
    update: async (locationId, entityId, params) =>
      await client
        .put<T>(`${locationUrl(locationId)}/${prefix}/${entityId}`, params)
        .then((r) => r.data),
    delete: async (locationId, entityId) =>
      await client
        .delete<number>(`${locationUrl(locationId)}/${prefix}/${entityId}`)
        .then((r) => r.data),
  };
}

const cache: {
  profile: null | Admin;
} = {
  profile: null,
};

export interface InviteCode {
  id: number;
  code: string;
  name: string;
  organization_id: number;
  used: boolean;
  used_at?: string;
  used_by?: string;
  created_at: string;
  updated_at: string;
}

export interface MenuSetting {
  id: number;
  location_id: number;
  type: string;
  title: string;
  description?: string;
  image_url?: string;
  link?: string;
  order: number;
  active: boolean;
  start_date?: string;
  end_date?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

const api = {
  baseUrl: env.api.baseURL,
  auth: {
    methods: async () =>
      await client.get<AuthMethod[]>("/auth/methods").then((r) => r.data),
    check: async (method: string, email: string) =>
      await client
        .post<boolean>("/auth/check", { method, email })
        .then((r) => r.data),
    basicAuth: async (email: string, password: string, redirect = "/") => {
      await client.post("/auth/login/basic/callback", { email, password });
      window.location.href = redirect;
    },
    emailAuth: async (email: string, redirect = "/") => {
      await client.post("/auth/login/email", { email, redirect });
    },
    login() {
      window.location.href = `/login?r=${encodeURIComponent(
        window.location.href
      )}`;
    },
    clearRecentLocations() {
      localStorage.removeItem("recent-locations");
    },
    async logout() {
      // Clear all localStorage
      localStorage.clear();

      // Clear all cookies by setting expired date
      const cookies = document.cookie.split(";");
      for (const cookie of cookies) {
        const eqPos = cookie.indexOf("=");
        const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
        document.cookie =
          name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
      }

      window.location.href = env.api.baseURL + "/auth/logout";
    },
    firebaseAuth: async (idToken: string, redirect = "/") => {
      console.log(
        "Calling Firebase auth API with token first 20 chars:",
        idToken.substring(0, 20) + "..."
      );
      try {
        const response = await client.post("/auth/login/firebase/callback", {
          idToken,
        });
        console.log(
          "Firebase auth API response:",
          response.status,
          response.statusText
        );
        window.location.href = redirect;
      } catch (error) {
        console.error("Firebase auth API error:", error);
        throw error;
      }
    },
  },

  profile: {
    get: async () => {
      if (!cache.profile) {
        cache.profile = await client
          .get<Admin>("/admin/profile")
          .then((r) => r.data);
      }
      return cache.profile;
    },
  },

  admins: {
    ...createEntityPath<Admin>("/admin/organizations/admins"),
    resendInvitation: async (adminId: number) => {
      const response = await client.post<{success: boolean; message: string}>(
        `/admin/organizations/admins/${adminId}/resend-invitation`
      );
      return response.data;
    },
  },

  locations: {
    ...createEntityPath<Location>("/admin/locations"),
    all: async () =>
      await client.get<Location[]>("/admin/locations/all").then((r) => r.data),
    pathSuggestions: async (locationId: number | string) =>
      await client
        .get<RuleSuggestions>(`${locationUrl(locationId)}/data/paths`)
        .then((r) => r.data),
    rebuildPathSuggestions: async (locationId: number | string) =>
      await client
        .post(`${locationUrl(locationId)}/data/paths/sync`)
        .then((r) => r.data),
    competitors: {
      add: (locationId: number, competitors: Competitor[]) =>
        client.post(`${locationUrl(locationId)}/competitors`, { competitors }),
      get: (locationId: number) =>
        client
          .get<{ competitors: Competitor[] }>(
            `${locationUrl(locationId)}/competitors`
          )
          .then((r) => {
            // Handle both response formats: { competitors: [...] } or direct array
            if (r.data && r.data.competitors) {
              return { data: r.data.competitors };
            }
            return r;
          }),
      remove: (locationId: number, placeId: string) =>
        client.delete(`${locationUrl(locationId)}/competitors/${placeId}`),
      triggerScrape: (locationId: number, data: { retailer_ids: string[] }) =>
        client.post(
          `${locationUrl(locationId)}/competitors/trigger-scrape`,
          data
        ),
      getMarketAnalysis: (locationId: number, category: string) =>
        client.get(
          `${locationUrl(locationId)}/competitors/market-analysis/${category}`
        ),
    },
    checkOnboardingStatus: (locationId: string | number) =>
      client
        .get(`${locationUrl(locationId)}/onboard-status`)
        .then((r) => r.data),
  },

  apiKeys: createLocationEntityPath<
    LocationApiKey,
    LocationApiKeyParams,
    Omit<LocationApiKeyParams, "scope">
  >("keys"),

  campaigns: {
    ...createLocationEntityPath<
      Campaign,
      CampaignCreateParams,
      CampaignUpdateParams | CampaignLaunchParams
    >("campaigns"),
    users: async (
      locationId: number | string,
      campaignId: number | string,
      params: SearchParams
    ) =>
      await client
        .get<SearchResult<CampaignUser>>(
          `${locationUrl(locationId)}/campaigns/${campaignId}/users`,
          { params }
        )
        .then((r) => r.data),
    duplicate: async (
      locationId: number | string,
      campaignId: number | string
    ) =>
      await client
        .post<Campaign>(
          `${locationUrl(locationId)}/campaigns/${campaignId}/duplicate`
        )
        .then((r) => r.data),
    generateSuggestions: async (locationId: number | string) =>
      await client
        .post<Campaign[]>(`${locationUrl(locationId)}/campaigns/suggestions`)
        .then((r) => r.data),
  },

  automations: {
    ...createLocationEntityPath<Journey>("automations"),
    create_with_steps: async (locationId: number | string, params: any) =>
      await client
        .post<Journey>(`${locationUrl(locationId)}/automations/create`, params)
        .then((r) => r.data),
    steps: {
      get: async (locationId: number | string, journeyId: number | string) =>
        await client
          .get<JourneyStepMap>(
            `${locationUrl(locationId)}/automations/${journeyId}/steps`
          )
          .then((r) => r.data),
      set: async (
        locationId: number | string,
        journeyId: number | string,
        stepData: JourneyStepMap
      ) =>
        await client
          .put<JourneyStepMap>(
            `${locationUrl(locationId)}/automations/${journeyId}/steps`,
            stepData
          )
          .then((r) => r.data),
      searchUsers: async (
        locationId: number | string,
        journeyId: number | string,
        stepId: number | string,
        params: SearchParams
      ) =>
        await client
          .get<SearchResult<JourneyUserStep>>(
            `${locationUrl(
              locationId
            )}/automations/${journeyId}/steps/${stepId}/users`,
            { params }
          )
          .then((r) => r.data),
    },
    entrances: {
      search: async (
        locationId: number | string,
        journeyId: number | string,
        params: SearchParams
      ) =>
        await client
          .get<SearchResult<JourneyEntranceDetail>>(
            `${locationUrl(locationId)}/automations/${journeyId}/entrances`,
            { params }
          )
          .then((r) => r.data),
    },
    generateSuggestions: async (locationId: number | string) =>
      await client
        .post<Journey[]>(`${locationUrl(locationId)}/automations/suggestions`)
        .then((r) => r.data),
  },

  orders: {
    ...createLocationEntityPath<Order>("orders"),
    getAll: async (locationId: number | string, params: any = {}) =>
      await client
        .get<SearchResult<Order>>(`${locationUrl(locationId)}/orders`, {
          params,
        })
        .then((r) => r.data),
    search: (locationId: number | string, params?: SearchParams) =>
      client
        .get<SearchResult<Order>>(`${locationUrl(locationId)}/orders`, {
          params,
        })
        .then((r) => r.data),
    getById: async (locationId: number | string, orderId: number | string) =>
      await client
        .get<Order>(`${locationUrl(locationId)}/orders/${orderId}`)
        .then((r) => r.data),
    updateStatus: async (
      locationId: number | string,
      orderId: number | string,
      data: { status: "pending" | "processing" | "completed" | "cancelled" }
    ) =>
      await client
        .put<Order>(`${locationUrl(locationId)}/orders/${orderId}/status`, data)
        .then((r) => r.data),
    cancel: async (locationId: number | string, orderId: number | string) =>
      await client
        .post<Order>(`${locationUrl(locationId)}/orders/${orderId}/cancel`)
        .then((r) => r.data),
    getUserOrders: async (
      locationId: number | string,
      userId: number | string,
      params: any = {}
    ) =>
      await client
        .get<SearchResult<Order>>(
          `${locationUrl(locationId)}/orders/user/${userId}`,
          { params }
        )
        .then((r) => r.data),
  },

  templates: {
    ...createLocationEntityPath<
      Template,
      TemplateCreateParams,
      TemplateUpdateParams
    >("templates"),
    preview: async (
      locationId: number | string,
      templateId: number | string,
      params: TemplatePreviewParams
    ) =>
      await client.post(
        `${locationUrl(locationId)}/templates/${templateId}/preview`,
        params
      ),
    proof: async (
      locationId: number | string,
      templateId: number | string,
      params: TemplateProofParams
    ) =>
      await client.post(
        `${locationUrl(locationId)}/templates/${templateId}/proof`,
        params
      ),
  },

  users: {
    ...createLocationEntityPath<User>("users"),
    search: (locationId: number | string, params?: SearchParams) =>
      client
        .get<SearchResult<User>>(`${locationUrl(locationId)}/users`, {
          params,
        })
        .then((r) => r.data),
    get: (locationId: number | string, userId: string | number) =>
      client
        .get<User>(`${locationUrl(locationId)}/users/${userId}`)
        .then((r) => r.data),
    upload: (locationId: number | string, file: File) => {
      const formData = new FormData();
      formData.append("users", file);
      return client
        .post<any>(`${locationUrl(locationId)}/users`, formData, {
          headers: { "Content-Type": "multipart/form-data" },
        })
        .then((r) => r.data);
    },
    lists: async (
      locationId: number | string,
      userId: number | string,
      params: SearchParams
    ) =>
      await client
        .get<SearchResult<List>>(
          `${locationUrl(locationId)}/users/${userId}/lists`,
          { params }
        )
        .then((r) => r.data),
    events: async (
      locationId: number | string,
      userId: number | string,
      params: SearchParams
    ) =>
      await client
        .get<SearchResult<UserEvent>>(
          `${locationUrl(locationId)}/users/${userId}/events`,
          { params }
        )
        .then((r) => r.data),
    subscriptions: async (
      locationId: number | string,
      userId: number | string,
      params: SearchParams
    ) =>
      await client
        .get<SearchResult<UserSubscription>>(
          `${locationUrl(locationId)}/users/${userId}/subscriptions`,
          { params }
        )
        .then((r) => r.data),
    updateSubscriptions: async (
      locationId: number | string,
      userId: number | string,
      subscriptions: SubscriptionParams[]
    ) =>
      await client
        .patch(
          `${locationUrl(locationId)}/users/${userId}/subscriptions`,
          subscriptions
        )
        .then((r) => r.data),
    automations: {
      search: async (
        locationId: number | string,
        userId: number | string,
        params: SearchParams
      ) =>
        await client
          .get<SearchResult<JourneyUserStep>>(
            `${locationUrl(locationId)}/users/${userId}/automations`,
            { params }
          )
          .then((r) => r.data),
    },
    getVectorizationStatus: (locationId: number) =>
      client
        .get(`${locationUrl(locationId)}/users/vectorization-status`)
        .then((r) => r.data),
    reVectorize: async (locationId: number, batchSize = 100) =>
      await client
        .post(`${locationUrl(locationId)}/users/re-vectorize`, {
          batch_size: batchSize,
        })
        .then((r) => r.data),
  },

  lists: {
    ...createLocationEntityPath<List, ListCreateParams, ListUpdateParams>(
      "lists"
    ),
    users: async (
      locationId: number | string,
      listId: number | string,
      params: SearchParams
    ) =>
      await client
        .get<SearchResult<User>>(
          `${locationUrl(locationId)}/lists/${listId}/users`,
          { params }
        )
        .then((r) => r.data),
    upload: async (
      locationId: number | string,
      listId: number | string,
      file: File
    ) => {
      const formData = new FormData();
      formData.append("file", file);
      await client.post(
        `${locationUrl(locationId)}/lists/${listId}/users`,
        formData
      );
    },
    bulkAddUsers: async (
      locationId: number | string,
      listId: number | string,
      userIds: number[]
    ) =>
      await client
        .post(`${locationUrl(locationId)}/lists/${listId}/bulk-add-users`, {
          user_ids: userIds,
        })
        .then((r) => r.data),
    generateSuggestions: async (locationId: number | string) =>
      await client
        .post<List[]>(`${locationUrl(locationId)}/lists/suggestions`)
        .then((r) => r.data),
  },

  locationAdmins: {
    search: async (locationId: number, params: SearchParams) =>
      await client
        .get<SearchResult<LocationAdmin>>(`${locationUrl(locationId)}/admins`, {
          params,
        })
        .then((r) => r.data),
    add: async (
      locationId: number,
      adminId: number,
      params: LocationAdminParams
    ) =>
      await client
        .put<LocationAdmin>(
          `${locationUrl(locationId)}/admins/${adminId}`,
          params
        )
        .then((r) => r.data),
    invite: async (locationId: number, params: LocationAdminInviteParams) =>
      await client
        .post<LocationAdmin>(`${locationUrl(locationId)}/admins`, params)
        .then((r) => r.data),
    get: async (locationId: number, adminId: number) =>
      await client
        .get<LocationAdmin>(`${locationUrl(locationId)}/admins/${adminId}`)
        .then((r) => r.data),
    remove: async (locationId: number, adminId: number) =>
      await client
        .delete(`${locationUrl(locationId)}/admins/${adminId}`)
        .then((r) => r.data),
    resendInvitation: async (locationId: number, adminId: number) => {
      const response = await client.post<{success: boolean; message: string}>(
        `${locationUrl(locationId)}/admins/${adminId}/resend-invitation`
      );
      return response.data;
    },
  },

  subscriptions: {
    ...createLocationEntityPath<
      Subscription,
      SubscriptionCreateParams,
      SubscriptionUpdateParams
    >("subscriptions"),
    canCreateLocation: async (organizationId: number) => {
      try {
        return await client
          .get<{ canCreate: boolean; message: string }>(
            `/misc/subscriptions/can-create-location/${organizationId}`
          )
          .then((r) => r.data);
      } catch (error) {
        console.error("Error checking location creation eligibility:", error);
        return {
          canCreate: false,
          message: "Error checking subscription status",
        };
      }
    },
    getStripeSubscription: async (
      organizationId: number
    ): Promise<StripeSubscription> => {
      return await client
        .get<StripeSubscriptionResponse>(
          `/misc/subscriptions/${organizationId}`
        )
        .then((r) => r.data.subscription);
    },
    cancelSubscription: async (subscriptionId: string): Promise<any> => {
      return await client
        .post<any>(`/misc/subscriptions/cancel/${subscriptionId}`)
        .then((r) => r.data);
    },
  },

  providers: {
    all: async (locationId: string | number) =>
      await client
        .get<Provider[]>(`${locationUrl(locationId)}/providers/all`)
        .then((r) => r.data),
    search: async (locationId: string | number, params: any) =>
      await client
        .get<SearchResult<Provider>>(`${locationUrl(locationId)}/providers`, {
          params,
        })
        .then((r) => r.data),
    options: async (locationId: string | number) =>
      await client
        .get<ProviderMeta[]>(`${locationUrl(locationId)}/providers/meta`)
        .then((r) => r.data),
    get: async (
      locationId: string | number,
      group: string,
      type: string,
      entityId: string | number
    ) =>
      await client
        .get<Provider>(
          `${locationUrl(locationId)}/providers/${group}/${type}/${entityId}`
        )
        .then((r) => r.data),
    create: async (locationId: string | number, params: any) =>
      await client
        .post<Provider>(`${locationUrl(locationId)}/providers`, params)
        .then((r) => r.data),
    update: async (
      locationId: string | number,
      entityId: string | number,
      params: any
    ) =>
      await client
        .patch<Provider>(
          `${locationUrl(locationId)}/providers/${entityId}`,
          params
        )
        .then((r) => r.data),
    delete: async (locationId: string | number, entityId: string | number) =>
      await client
        .delete<Provider>(`${locationUrl(locationId)}/providers/${entityId}`)
        .then((r) => r.data),
    searchGlobal: async (params: any) =>
      await client
        .get<SearchResult<Provider>>(`/providers/global`, params)
        .then((r) => r.data),
    globalOptions: async () =>
      await client
        .get<ProviderMeta[]>(`/providers/global/meta`)
        .then((r) => r.data),
    getGlobal: async (group: string, type: string, entityId: string | number) =>
      await client
        .get<Provider>(`/providers/global/${group}/${type}/${entityId}`)
        .then((r) => r.data),
    createGlobal: async (params: any) =>
      await client
        .post<Provider>(`/providers/global`, params)
        .then((r) => r.data),
    updateGlobal: async (entityId: string | number, params: any) =>
      await client
        .patch<Provider>(`/providers/global/${entityId}`, params)
        .then((r) => r.data),
    deleteGlobal: async (entityId: string | number) =>
      await client
        .delete<Provider>(`/providers/global/${entityId}`)
        .then((r) => r.data),
  },

  images: {
    ...createLocationEntityPath<Image>("images"),
    create: async (locationId: number | string, image: File) => {
      const formData = new FormData();
      formData.append("image", image);
      await client.post(`${locationUrl(locationId)}/images`, formData);
    },
  },

  resources: {
    all: async (locationId: number | string, type = "font") =>
      await client
        .get<Resource[]>(`${locationUrl(locationId)}/resources?type=${type}`)
        .then((r) => r.data),
    create: async (locationId: number | string, params: Partial<Resource>) =>
      await client
        .post<Resource>(`${locationUrl(locationId)}/resources`, params)
        .then((r) => r.data),
    delete: async (locationId: number | string, id: number) =>
      await client
        .delete<number>(`${locationUrl(locationId)}/resources/${id}`)
        .then((r) => r.data),
  },

  tags: {
    ...createLocationEntityPath<Tag>("tags"),
    used: async (locationId: number | string, entity: string) =>
      await client
        .get<Tag[]>(`${locationUrl(locationId)}/tags/used/${entity}`)
        .then((r) => r.data),
    assign: async (
      locationId: number | string,
      entity: string,
      entityId: number,
      tags: string[]
    ) =>
      await client
        .put<string[]>(`${locationUrl(locationId)}/tags/assign`, {
          entity,
          entityId,
          tags,
        })
        .then((r) => r.data),
    all: async (locationId: number | string) =>
      await client
        .get<Tag[]>(`${locationUrl(locationId)}/tags/all`)
        .then((r) => r.data),
  },

  organizations: {
    get: async () =>
      await client
        .get<Organization>("/admin/organizations")
        .then((r) => r.data),
    update: async (
      id: number | string,
      params: Omit<OrganizationUpdateParams, "onboarding_complete">
    ) =>
      await client
        .patch<Organization>(`/admin/organizations/${id}`, params)
        .then((r) => r.data),
    delete: async () =>
      await client.delete("/admin/organizations").then((r) => r.data),
    metrics: async () =>
      await client
        .get<QueueMetric>("/admin/organizations/performance/queue")
        .then((r) => r.data),
    jobs: async () =>
      await client
        .get<string[]>("/admin/organizations/performance/jobs")
        .then((r) => r.data),
    jobPerformance: async (job: string) =>
      await client
        .get<Metric[]>(`/admin/organizations/performance/jobs/${job}`)
        .then((r) => r.data),
    failed: async () =>
      await client
        .get<any>("/admin/organizations/performance/failed")
        .then((r) => r.data),
    verifySender: async (params: { email: string }) =>
      await client
        .post<{ status: string; message: string }>(
          "/admin/organizations/verify-sender",
          params
        )
        .then((r) => r.data),
  },

  locales: createLocationEntityPath<Locale>("locales"),

  pos: {
    ...createLocationEntityPath<POSData>("pos/data"),
    search: (locationId: number, params: SearchParams) =>
      client
        .get<SearchResult<POSData>>(`${locationUrl(locationId)}/pos/data`, {
          params,
        })
        .then((r) => r.data),
    upload: (locationId: number, formData: FormData) =>
      client
        .post(`${locationUrl(locationId)}/pos/import`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((r) => r.data),
    testConnection: (
      locationId: number | string,
      posType: string,
      credentials: any
    ) =>
      client
        .post<PosConnectionResult>(
          `${locationUrl(locationId)}/pos/test-connection`,
          {
            posType,
            credentials,
          }
        )
        .then((r) => r.data),
    connectSystem: (
      locationId: number | string,
      posType: string,
      credentials: any
    ) =>
      client
        .post<PosConnectionResult>(`${locationUrl(locationId)}/pos/connect`, {
          posType,
          credentials,
        })
        .then((r) => r.data),
    saveData: (locationId: number | string, data: any) =>
      client
        .post(`${locationUrl(locationId)}/pos/save-normalized-data`, data)
        .then((r) => r.data),
    analyze: (locationId: number, query: string) =>
      client
        .post(`${locationUrl(locationId)}/pos/analytics/analyze`, { query })
        .then((r) => r.data),
    testVectorSearch: (locationId: number, query: string, limit?: number) =>
      client
        .post(`${locationUrl(locationId)}/pos/analytics/vector-search`, {
          query,
          limit,
        })
        .then((r) => r.data),
    reVectorize: (locationId: number, batch_size?: number) =>
      client
        .post(`${locationUrl(locationId)}/pos/re-vectorize`, { batch_size })
        .then((r) => r.data),
    getVectorizationStatus: (locationId: number) =>
      client
        .get(`${locationUrl(locationId)}/pos/vectorization-status`)
        .then((r) => r.data),
  },
  chat: {
    ...createLocationEntityPath<null>("chat-messages"),
  },
  dashboard: {
    get: (locationId: number, params: any) =>
      client
        .get<any>(`${locationUrl(locationId)}/dashboard`, { params })
        .then((r) => r.data),
    generateInsights: (locationId: number) =>
      client
        .post<any>(`${locationUrl(locationId)}/dashboard/insights/generate`)
        .then((r) => r.data),
  },
  products: {
    ...createLocationEntityPath<Product>("products"),

    search: async (locationId: number | string, params?: SearchParams) =>
      await client
        .get<SearchResult<Product>>(`${locationUrl(locationId)}/products`, {
          params,
        })
        .then((r) => r.data),

    get: async (locationId: number | string, id: string | number) =>
      await client
        .get<Product>(`${locationUrl(locationId)}/products/${id}`)
        .then((r) => r.data),

    patch: async (
      locationId: number | string,
      id: number,
      data: Partial<Product>
    ) =>
      await client
        .patch<Product>(`${locationUrl(locationId)}/products/${id}`, data)
        .then((r) => r.data),

    batchPatch: async (
      locationId: number | string,
      products: ProductParams[]
    ) =>
      await client
        .patch<void>(`${locationUrl(locationId)}/products`, products)
        .then((r) => r.data),

    upload: async (locationId: number | string, formData: FormData) =>
      await client
        .post<Product>(`${locationUrl(locationId)}/products/upload`, formData, {
          headers: { "Content-Type": "multipart/form-data" },
        })
        .then((r) => r.data),
    delete: async (locationId: number | string, id: string | number) =>
      await client
        .delete(`${locationUrl(locationId)}/products/${id}`)
        .then((r) => r.data),
    bulkDelete: async (
      locationId: number | string,
      productIds: number[]
    ): Promise<BulkDeleteResponse> =>
      await client
        .delete(`${locationUrl(locationId)}/products/bulk-delete`, {
          data: { product_ids: productIds },
        })
        .then((r) => r.data),
    create: async (locationId: number | string, product: ProductParams) =>
      await client
        .post<Product>(`${locationUrl(locationId)}/products`, product)
        .then((r) => r.data),
    update: async (
      locationId: number | string,
      id: string | number,
      product: ProductParams
    ) =>
      await client
        .patch<Product>(`${locationUrl(locationId)}/products/${id}`, product)
        .then((r) => r.data),
    enhance: async (
      locationId: number | string,
      params?: {
        limit?: number;
        skip?: number;
        meta_sku?: string;
        async?: boolean;
      }
    ) =>
      await client
        .post<{
          message: string;
          enhanced_count?: number;
          vectorized_count?: number;
          failed_vectorization?: number;
          status?: string;
          count?: number;
          async?: boolean;
        }>(`${locationUrl(locationId)}/products/enhance`, null, {
          params,
        })
        .then((r) => r.data),
    syncMarketplace: async (
      locationId: number | string,
      retailerId: string,
      options?: { skip_scrape?: boolean }
    ) =>
      await client
        .post<{
          success: boolean;
          message: string;
          syncedProducts?: number;
          aiEnhanced?: boolean;
          reindexed?: boolean;
          scrapeInitiated?: boolean;
          productCount?: number;
          estimatedScrapeTime?: number;
          status?: string;
        }>(`${locationUrl(locationId)}/products/sync-marketplace`, {
          retailer_id: retailerId,
          ...options,
        })
        .then((r) => r.data),
    getVectorizationStatus: (locationId: number) =>
      client
        .get(`${locationUrl(locationId)}/products/vectorization-status`)
        .then((r) => r.data),
    reVectorize: async (
      locationId: number,
      options: { batch_size?: number; clean_start?: boolean } = {}
    ) =>
      await client
        .post(`${locationUrl(locationId)}/products/re-vectorize`, options)
        .then((r) => r.data),
    getEnhancementStatus: (locationId: number) =>
      client
        .get(`${locationUrl(locationId)}/products/enhancement-status`)
        .then((r) => r.data),
    getCOAs: async (locationId: number | string, productId: string) =>
      await client
        .get<{ data: CoaData[]; count: number }>(
          `${locationUrl(locationId)}/coa/product/${productId}`
        )
        .then((r) => r.data),
  },
  featuredProducts: {
    getFeatured: async (
      locationId: number | string,
      params?: { top_picks_only?: boolean; active_only?: boolean }
    ) =>
      await client
        .get(`${locationUrl(locationId)}/products/featured`, { params })
        .then((r) => r.data),

    addFeatured: async (
      locationId: number | string,
      data: {
        product_id: number;
        is_top_pick?: boolean;
        sort_order?: number;
        active?: boolean;
      }
    ) =>
      await client
        .post(`${locationUrl(locationId)}/products/featured`, data)
        .then((r) => r.data),

    updateFeatured: async (
      locationId: number | string,
      featuredId: number,
      data: { is_top_pick?: boolean; sort_order?: number; active?: boolean }
    ) =>
      await client
        .patch(
          `${locationUrl(locationId)}/products/featured/${featuredId}`,
          data
        )
        .then((r) => r.data),

    removeFeatured: async (locationId: number | string, featuredId: number) =>
      await client
        .delete(`${locationUrl(locationId)}/products/featured/${featuredId}`)
        .then((r) => r.data),

    bulkRemoveFeatured: async (
      locationId: number | string,
      productIds: number[]
    ) =>
      await client
        .delete(`${locationUrl(locationId)}/products/featured/bulk-remove`, {
          data: { product_ids: productIds },
        })
        .then((r) => r.data),

    bulkUpdateFeatured: async (
      locationId: number | string,
      updates: Array<{
        id: number;
        is_top_pick?: boolean;
        sort_order?: number;
        active?: boolean;
      }>
    ) =>
      await client
        .patch(`${locationUrl(locationId)}/products/featured/bulk-update`, {
          updates,
        })
        .then((r) => r.data),
  },
  insights: {
    get: async (locationId: number | string): Promise<Insight[]> =>
      await client
        .get<Insight[]>(`${locationUrl(locationId)}/dashboard/insights`)
        .then((r) => r.data),
    generate: async (
      locationId: number | string,
      model?: string
    ): Promise<Insight[]> =>
      await client
        .post<Insight[]>(
          `${locationUrl(locationId)}/dashboard/insights/generate`,
          { model }
        )
        .then((r) => r.data),
    getAutomationPlan: async (
      locationId: number | string,
      insightId: number,
      model?: string,
      imageQuality: string = "HD"
    ): Promise<any> =>
      await client
        .post<any>(
          `${locationUrl(
            locationId
          )}/dashboard/insights/${insightId}/automation-plan`,
          { model, imageQuality }
        )
        .then((r) => r.data),
    updateStatus: async (
      locationId: number | string,
      insightId: number,
      status: string
    ): Promise<void> =>
      await client
        .put(
          `${locationUrl(locationId)}/dashboard/insights/${insightId}/status`,
          { status }
        )
        .then((r) => r.data),
    executeStep: async (step: any, token?: string): Promise<any> =>
      await client
        .post(
          `${locationUrl(step.location_id)}/dashboard/insights/execute-step`,
          { step },
          {
            headers: token ? { Authorization: `Bearer ${token}` } : undefined,
          }
        )
        .then((r) => r.data),
    regenerateStep: async (
      locationId: number | string,
      insightId: number,
      params: {
        stepIndex?: number;
        itemKey?: string;
        model?: string;
        prompt?: string;
        imageQuality?: string;
      }
    ): Promise<any> => {
      // Validate that at least one of stepIndex or itemKey is provided
      if (params.stepIndex === null && !params.itemKey) {
        console.error(
          "Invalid parameters for regenerateStep: must provide either stepIndex or itemKey"
        );
        throw new Error(
          "Must provide either a valid step index (number) or item key (string)"
        );
      }

      // Convert null stepIndex to undefined to avoid sending it in the request
      const validatedParams = {
        ...params,
        stepIndex: params.stepIndex === null ? undefined : params.stepIndex,
      };

      console.log(
        "Sending regenerateStep request with params:",
        validatedParams
      );

      return await client
        .post(
          `${locationUrl(
            locationId
          )}/dashboard/insights/${insightId}/regenerate-step`,
          validatedParams
        )
        .then((r) => r.data);
    },
    sendFeedback: async (
      locationId: number | string,
      insightId: number,
      feedback: { success: boolean; error?: string; steps?: any[] }
    ): Promise<void> => {
      const maxRetries = 3;
      const retryDelay = 2000; // 2 seconds

      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          console.log(
            `[FEEDBACK] Attempt ${attempt}/${maxRetries} for insight ${insightId}`
          );

          const response = await client.post(
            `${locationUrl(
              locationId
            )}/dashboard/insights/${insightId}/feedback`,
            feedback,
            {
              timeout: 180000, // 3 minutes timeout
            }
          );

          console.log(
            `[FEEDBACK] Successfully sent feedback for insight ${insightId}`
          );
          return response.data;
        } catch (error: any) {
          console.error(
            `[FEEDBACK] Attempt ${attempt} failed for insight ${insightId}:`,
            error.message
          );

          // If it's the last attempt or not a timeout/network error, throw the error
          if (
            attempt === maxRetries ||
            (error.code !== 504 &&
              error.code !== "ECONNABORTED" &&
              !error.message.includes("timeout"))
          ) {
            throw error;
          }

          // Wait before retrying
          if (attempt < maxRetries) {
            console.log(`[FEEDBACK] Retrying in ${retryDelay}ms...`);
            await new Promise((resolve) => setTimeout(resolve, retryDelay));
          }
        }
      }
    },
  },
  documents: {
    upload: async (locationId: number | string, formData: FormData) => {
      const response = await client
        .post(`${locationUrl(locationId)}/documents/upload`, formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((r) => r.data);

      if (!response.ok) {
        throw new Error("Failed to upload document");
      }
      return response.json();
    },

    getAnalysis: async (locationId: number | string, documentId: number) => {
      const response = await client
        .get(`${locationUrl(locationId)}/documents/analysis/${documentId}`)
        .then((r) => r.data);
      if (!response.ok) {
        throw new Error("Failed to get document analysis");
      }
      return response.json();
    },
  },
  system: systemModule(client),
  getChats: async (locationId: number | string) =>
    await client
      .get(`${locationUrl(locationId)}/chats`)
      .then((r) => r.data || []),
  createChat: async (locationId: number | string) =>
    await client
      .post(`${locationUrl(locationId)}/chats`, {
        name: "New Chat",
      })
      .then((r) => r.data),
  getChatMessages: async (chatId: string, locationId: number | string) =>
    await client
      .get(`${locationUrl(locationId)}/chats/${chatId}/messages`)
      .then((r) => r.data.messages || []),
  sendMessage: async ({
    message,
    agentId,
    chatId,
    locationId,
    useGenie = false,
  }: {
    message: string;
    agentId: string;
    chatId?: string;
    locationId: number | string;
    useGenie?: boolean;
  }) => {
    // If no chatId, create a new chat first
    let targetChatId = chatId;
    if (!targetChatId) {
      const newChat = await client
        .post(`${locationUrl(locationId)}/chats`, {
          name: "New Chat",
        })
        .then((r) => r.data);
      targetChatId = newChat.chat_id;
    }

    if (!useGenie) {
      return await client
        .post(`${locationUrl(locationId)}/chats/${targetChatId}/responses`, {
          message,
          agent_id: agentId,
        })
        .then((r) => r.data);
    } else {
      // First add the message to our regular chat system
      await client.post(
        `${locationUrl(locationId)}/chats/${targetChatId}/messages`,
        {
          message,
          agent_id: agentId,
        }
      );

      // Then handle Genie logic as needed
      return await client
        .post(`${locationUrl(locationId)}/chats/${targetChatId}/responses`, {
          message,
          agent_id: agentId,
          use_genie: true,
        })
        .then((r) => r.data);
    }
  },
  renameChat: async (
    chatId: string,
    newName: string,
    locationId: number | string
  ) =>
    await client.put(`${locationUrl(locationId)}/chats/${chatId}`, {
      name: newName,
    }),
  getChatSummary: async (chatId: string, locationId: number | string) =>
    await client
      .get(`${locationUrl(locationId)}/chats/${chatId}/summary`)
      .then((r) => r.data),
  generateChatInsights: async (chatId: string, locationId: number | string) =>
    await client
      .post(`${locationUrl(locationId)}/chats/${chatId}/insights`)
      .then((r) => r.data),
  getChatFiles: async (chatId: string, locationId: number | string) =>
    await client
      .get(`${locationUrl(locationId)}/chats/${chatId}/files`)
      .then((r) => r.data || []),
  uploadFile: async (
    chatId: string,
    locationId: number | string,
    file: File
  ) => {
    const formData = new FormData();
    formData.append("file", file);
    return await client
      .post(`${locationUrl(locationId)}/chats/${chatId}/files`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      })
      .then((r) => r.data);
  },
  getChatInsights: async (locationId: number | string, timeframe = "30d") =>
    await client
      .get(`${locationUrl(locationId)}/chats/insights`, {
        params: { timeframe },
      })
      .then((r) => r.data || []),
  supabase: {
    uploadData: (locationId: number | string, data: any) =>
      client
        .post(`${locationUrl(locationId)}/supabase/upload`, data)
        .then((r) => r.data),
    getStatus: (locationId: number | string) =>
      client
        .get(`${locationUrl(locationId)}/supabase/status`)
        .then((r) => r.data),
    getTableInfo: (locationId: number | string, tableName: string) =>
      client
        .get(`${locationUrl(locationId)}/supabase/tables/${tableName}`)
        .then((r) => r.data),
    testConnection: (locationId: number | string) =>
      client
        .get(`${locationUrl(locationId)}/supabase/test-connection`)
        .then((r) => r.data),
  },
  agents: {
    getAvailability: async (locationId: number | string) =>
      await client
        .get(`${locationUrl(locationId)}/agents/availability`)
        .then((r) => r.data),

    getDefinitions: async (locationId: number | string) =>
      await client
        .get(`${locationUrl(locationId)}/agents/definitions`)
        .then((r) => r.data),

    getAvailable: async (locationId: number | string) =>
      await client
        .get(`${locationUrl(locationId)}/agents/available`)
        .then((r) => r.data),
  },
  coa: {
    search: async (locationId: number | string, params?: SearchParams) =>
      await client
        .get<SearchResult<CoaData>>(`${locationUrl(locationId)}/coa`, {
          params,
        })
        .then((r) => r.data),

    get: async (locationId: number | string, id: string | number) =>
      await client
        .get<CoaData>(`${locationUrl(locationId)}/coa/${id}`)
        .then((r) => r.data),

    getForProduct: async (locationId: number | string, productId: string) =>
      await client
        .get<{ data: CoaData[]; count: number }>(
          `${locationUrl(locationId)}/coa/product/${productId}`
        )
        .then((r) => r.data),

    upload: async (locationId: number | string, formData: FormData) =>
      await client
        .post<any>(`${locationUrl(locationId)}/coa/import`, formData, {
          headers: { "Content-Type": "multipart/form-data" },
        })
        .then((r) => r.data),

    import: async (locationId: number | string, coaData: any) =>
      await client
        .post<any>(`${locationUrl(locationId)}/coa/import`, {
          coa_data: coaData,
        })
        .then((r) => r.data),
  },
  customPrompts: {
    list: async (locationId: number | string) =>
      await client
        .get(`${locationUrl(locationId)}/custom-prompts`)
        .then((r) => r.data),

    get: async (locationId: number | string, promptId: number) =>
      await client
        .get(`${locationUrl(locationId)}/custom-prompts/${promptId}`)
        .then((r) => r.data),

    create: async (
      locationId: number | string,
      data: { name: string; content: string; is_active: boolean }
    ) =>
      await client
        .post(`${locationUrl(locationId)}/custom-prompts`, data)
        .then((r) => r.data),

    update: async (
      locationId: number | string,
      promptId: number,
      data: { name: string; content: string; is_active: boolean }
    ) =>
      await client
        .put(`${locationUrl(locationId)}/custom-prompts/${promptId}`, data)
        .then((r) => r.data),

    delete: async (locationId: number | string, promptId: number) =>
      await client
        .delete(`${locationUrl(locationId)}/custom-prompts/${promptId}`)
        .then((r) => r.data),

    toggle: async (locationId: number | string, promptId: number) =>
      await client
        .post(`${locationUrl(locationId)}/custom-prompts/${promptId}/toggle`)
        .then((r) => r.data),
  },

  // Add market analysis section for onboarding
  marketAnalysis: {
    getOnboardingAnalysis: async (
      competitors: Competitor[],
      category: string
    ) => {
      return await client
        .post(`/misc/onboarding/market-analysis`, {
          competitors,
          category,
        })
        .then((r) => r.data);
    },
  },

  invites: {
    getAll: async () => {
      return await client
        .get<InviteCode[]>("/admin/organizations/invite-codes")
        .then((r) => r.data);
    },

    create: async (name: string, prefix?: string) => {
      return await client
        .post<InviteCode>("/admin/organizations/invite-codes", { name, prefix })
        .then((r) => r.data);
    },

    delete: async (id: number) => {
      return await client
        .delete<{ success: boolean }>(`/admin/organizations/invite-codes/${id}`)
        .then((r) => r.data);
    },

    validateCode: async (code: string) => {
      return await client
        .post<{ valid: boolean; code?: InviteCode; error?: string }>(
          "/misc/invite-codes/validate",
          { code }
        )
        .then((r) => r.data);
    },

    useCode: async (code: string, email: string) => {
      return await client
        .post<{ success: boolean; code: InviteCode }>(
          "/misc/invite-codes/use",
          { code, email }
        )
        .then((r) => r.data);
    },
  },

  events: {
    // Get events for a location
    getLocationEvents: async (
      locationId: number | string,
      params: {
        page?: number;
        limit?: number;
        search?: string;
        future_only?: boolean;
      } = {}
    ) => {
      return await client
        .get(`${locationUrl(locationId)}/events`, { params })
        .then((r) => r.data);
    },

    // Get specific event by ID
    getEvent: async (locationId: number | string, eventId: string) => {
      return await client
        .get(`${locationUrl(locationId)}/events/${eventId}`)
        .then((r) => r.data);
    },

    // Create new event
    createEvent: async (
      locationId: number | string,
      eventData: {
        event_name: string;
        category?: string[];
        start_time?: string;
        timezone?: string;
        host?: string;
        starting_price?: string;
        address?: string;
        city?: string;
        state?: string;
        postal_code?: string;
        image?: string;
        url?: string;
      }
    ) => {
      return await client
        .post(`${locationUrl(locationId)}/events`, eventData)
        .then((r) => r.data);
    },

    // Update event
    updateEvent: async (
      locationId: number | string,
      eventId: string,
      eventData: {
        event_name?: string;
        category?: string[];
        start_time?: string;
        timezone?: string;
        host?: string;
        starting_price?: string;
        address?: string;
        city?: string;
        state?: string;
        postal_code?: string;
        image?: string;
        url?: string;
      }
    ) => {
      return await client
        .put(`${locationUrl(locationId)}/events/${eventId}`, eventData)
        .then((r) => r.data);
    },

    // Delete event
    deleteEvent: async (locationId: number | string, eventId: string) => {
      return await client
        .delete(`${locationUrl(locationId)}/events/${eventId}`)
        .then((r) => r.data);
    },

    // Search public events (existing endpoint)
    searchPublicEvents: async (params: {
      query?: string;
      city?: string;
      state?: string;
      limit?: number;
      page?: number;
    }) => {
      return await client
        .post("/misc/events/search", params)
        .then((r) => r.data);
    },
  },

  menuSettings: createLocationEntityPath<MenuSetting>("menu-settings"),
};

export const { menuSettings: menuSettingsApi } = api;

export default api;

(window as any).API = api;
